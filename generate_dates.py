#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期数据生成器
生成从20240827到20250708的日期数据
格式：每行一个日期，每19行用空行隔开，总共1501条数据
"""

from datetime import datetime, timedelta
import random

def generate_date_data():
    """生成日期数据"""
    start_date = datetime(2024, 8, 27)
    end_date = datetime(2025, 7, 8)

    # 计算总天数
    total_days = (end_date - start_date).days + 1
    print(f"日期范围：{start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')}")
    print(f"总天数：{total_days}")

    # 生成所有可能的日期
    all_dates = []
    current_date = start_date
    while current_date <= end_date:
        all_dates.append(current_date.strftime('%Y%m%d'))
        current_date += timedelta(days=1)

    # 生成1501条数据
    target_count = 1501
    generated_dates = []

    # 每19行为一组，每组包含3-4天的日期数据
    group_size = 19
    total_groups = (target_count + group_size - 1) // group_size  # 向上取整

    print(f"需要生成组数：{total_groups}")
    print(f"目标数据条数：{target_count}")

    dates_generated = 0

    for group_num in range(total_groups):
        # 每组随机选择3-4天
        days_in_group = random.randint(3, 4)

        # 从可用日期中随机选择
        if len(all_dates) >= days_in_group:
            selected_dates = random.sample(all_dates, days_in_group)
            selected_dates.sort()  # 按日期排序（从早到晚）
        else:
            # 如果剩余日期不足，使用所有剩余日期
            selected_dates = all_dates.copy()

        # 为这组生成19行数据（或剩余需要的数据）
        lines_needed = min(group_size, target_count - dates_generated)

        # 计算每个日期需要重复的次数，确保相同日期挨在一起
        dates_per_day = lines_needed // len(selected_dates)
        extra_lines = lines_needed % len(selected_dates)

        group_dates = []
        for i, date in enumerate(selected_dates):
            # 每个日期的基础重复次数
            repeat_count = dates_per_day
            # 前面的日期多分配一行（如果有余数）
            if i < extra_lines:
                repeat_count += 1

            # 添加该日期的所有重复
            for _ in range(repeat_count):
                group_dates.append(date)

        generated_dates.extend(group_dates)
        dates_generated += len(group_dates)

        # 如果不是最后一组，添加空行
        if group_num < total_groups - 1 and dates_generated < target_count:
            generated_dates.append("")

        # 如果已经生成足够的数据，退出
        if dates_generated >= target_count:
            break

    return generated_dates[:target_count + (target_count - 1) // group_size]  # 包含空行

def save_to_file(dates, filename="date_data.txt"):
    """保存日期数据到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        for date in dates:
            f.write(date + '\n')
    
    print(f"数据已保存到文件：{filename}")
    print(f"总行数（包含空行）：{len(dates)}")
    
    # 统计非空行数
    non_empty_lines = [d for d in dates if d.strip()]
    print(f"实际日期数据行数：{len(non_empty_lines)}")

def main():
    """主函数"""
    print("开始生成日期数据...")
    dates = generate_date_data()
    save_to_file(dates)
    print("生成完成！")

if __name__ == "__main__":
    main()
