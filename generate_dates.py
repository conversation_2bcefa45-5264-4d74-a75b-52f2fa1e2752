#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日期数据生成器
生成从20240827到20250708的日期数据
格式：每行一个日期，每19行用空行隔开，总共1501条数据
"""

from datetime import datetime, timedelta
import random

def generate_date_data():
    """生成日期数据"""
    start_date = datetime(2024, 8, 27)
    end_date = datetime(2025, 7, 8)

    # 计算总天数
    total_days = (end_date - start_date).days + 1
    print(f"日期范围：{start_date.strftime('%Y%m%d')} 到 {end_date.strftime('%Y%m%d')}")
    print(f"总天数：{total_days}")

    # 生成1501条数据
    target_count = 1501
    generated_dates = []

    # 每19行为一组
    group_size = 19
    total_groups = (target_count + group_size - 1) // group_size  # 向上取整

    print(f"需要生成组数：{total_groups}")
    print(f"目标数据条数：{target_count}")

    dates_generated = 0
    current_date = start_date

    for group_num in range(total_groups):
        # 为这组生成19行数据（或剩余需要的数据）
        lines_needed = min(group_size, target_count - dates_generated)

        group_dates = []
        lines_in_group = 0

        # 生成连续的日期，每个日期随机重复1-4次
        while lines_in_group < lines_needed:
            # 如果超出日期范围，重新从开始日期循环
            if current_date > end_date:
                current_date = start_date

            # 随机决定当前日期重复次数（1-4次）
            max_repeats = min(4, lines_needed - lines_in_group)
            repeat_count = random.randint(1, max_repeats)

            # 添加当前日期的重复
            date_str = current_date.strftime('%Y%m%d')
            for _ in range(repeat_count):
                group_dates.append(date_str)
                lines_in_group += 1
                if lines_in_group >= lines_needed:
                    break

            # 移动到下一天
            current_date += timedelta(days=1)

        generated_dates.extend(group_dates)
        dates_generated += len(group_dates)

        # 如果不是最后一组，添加空行
        if group_num < total_groups - 1 and dates_generated < target_count:
            generated_dates.append("")

        # 如果已经生成足够的数据，退出
        if dates_generated >= target_count:
            break

    return generated_dates

def save_to_file(dates, filename="date_data.txt"):
    """保存日期数据到文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        for date in dates:
            f.write(date + '\n')

    print(f"数据已保存到文件：{filename}")
    print(f"总行数（包含空行）：{len(dates)}")

    # 统计非空行数
    non_empty_lines = [d for d in dates if d.strip()]
    print(f"实际日期数据行数：{len(non_empty_lines)}")

def main():
    """主函数"""
    print("开始生成日期数据...")
    dates = generate_date_data()
    save_to_file(dates)
    print("生成完成！")

if __name__ == "__main__":
    main()
